from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen, FadeTransition
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.clock import Clock
from kivy.animation import Animation
from kivy.properties import StringProperty, ListProperty
from kivy.core.text import LabelBase
from kivy.resources import resource_add_path
from kivy.core.image import Image as CoreImage
import os
import platform
import time


# ---------- 字体配置 ----------
# 添加字体资源路径 - 使用系统字体路径
# Windows 7 兼容性处理
if platform.system() == 'Windows':
    try:
        fonts_path = os.path.join(os.environ['WINDIR'], 'Fonts')
        resource_add_path(fonts_path)
        
        # 检查字体文件是否存在
        msyh_path = os.path.join(fonts_path, 'msyh.ttc')
        simhei_path = os.path.join(fonts_path, 'simhei.ttf')
        
        if os.path.exists(msyh_path):
            primary_font = msyh_path
        elif os.path.exists(simhei_path):
            primary_font = simhei_path
        else:
            # 找不到中文字体时的备用方案
            primary_font = None
            print("警告: 找不到中文字体文件")
            
        # 注册中文字体
        if primary_font:
            LabelBase.register('chinese_font', primary_font)
    except Exception as e:
        print(f"字体配置错误: {e}")
        # 出错时不注册字体，应用将使用默认字体

# 获取图片尺寸
img_path = 'picture/p1.png'
try:
    img = CoreImage(img_path)
    img_width, img_height = img.texture.width, img.texture.height
    print(f"图片尺寸: {img_width} x {img_height}")
except Exception as e:
    print(f"无法获取图片尺寸: {e}")
    img_width, img_height = 160, 70  # 默认尺寸

# ---------- 全局配置 ----------
CFG = {
    'main': {'w': img_width, 'h': img_height},  # 使用图片尺寸
    'second': {'w': 1600, 'h': 900},
    'pos': {'x': 500, 'y': 200},
}

# ---------- 工具 ----------
def set_window(size_key: str, center: bool = False):
    """根据 key 设置窗口大小与位置"""
    w, h = CFG[size_key]['w'], CFG[size_key]['h']
    Window.size = (w, h)
    
    # Windows 7 兼容性处理
    try:
        if center:
            # 确保窗口居中，考虑窗口尺寸
            # Windows 7 可能无法正确获取 system_size
            try:
                screen_width, screen_height = Window.system_size
            except:
                # 备用方案: 使用固定值或设置一个合理的默认值
                screen_width, screen_height = 1920, 1080
                print("警告: 无法获取屏幕尺寸，使用默认值")
                
            Window.left = max(0, (screen_width - w) // 2)
            Window.top = max(0, (screen_height - h) // 2)
        else:
            Window.left, Window.top = CFG['pos']['x'], CFG['pos']['y']
    except Exception as e:
        print(f"窗口位置设置错误: {e}")
        # 错误时使用默认位置


# ---------- 加载 KV 文件 ----------
# Python 3.8.7 兼容性处理
try:
    Builder.load_file('app.kv')
except Exception as e:
    print(f"KV文件加载错误: {e}")
    # 可以在这里添加备用的UI创建方法


# ---------- 屏幕 ----------
class MainScreen(Screen):
    def __init__(self, **kw):
        super().__init__(**kw)

    def on_pre_enter(self):
        set_window('main')
        # 添加进入动画
        for child in self.children:
            child.opacity = 0
            anim = Animation(opacity=1, duration=0.5)
            anim.start(child)

    def goto_second(self, *args):
        self.manager.transition = FadeTransition(duration=0.5)
        self.manager.current = 'second'


class SecondScreen(Screen):
    def __init__(self, **kw):
        super().__init__(**kw)
        # 启动时钟更新
        Clock.schedule_interval(self.update_clock, 1)

    def update_clock(self, dt):
        # 更新时钟显示
        import time
        time_btn = self.ids.get('time_display')
        if time_btn:
            time_btn.text = time.strftime("%H:%M:%S")

    def on_pre_enter(self):
        set_window('second', center=True)
        # 打印窗口信息用于调试
        try:
            print(f"窗口位置: {Window.left}, {Window.top}")
            print(f"系统尺寸: {Window.system_size}")
            print(f"窗口尺寸: {Window.size}")
        except Exception as e:
            print(f"获取窗口信息错误: {e}")

    def goto_main(self, *args):
        self.manager.transition = FadeTransition(duration=0.5)
        self.manager.current = 'main'

    def exit_app(self, *args):
        """完全退出应用程序"""
        # 添加退出前的渐变动画
        try:
            fade_out = Animation(opacity=0, duration=0.5)
            fade_out.bind(on_complete=lambda *x: App.get_running_app().stop())
            fade_out.start(self)
        except:
            # 如果动画失败，直接退出
            App.get_running_app().stop()


# ---------- App ----------
class HelloApp(App):
    def build(self):
        # 无边框、置顶、不可调整
        try:
            Window.borderless = True
            Window.always_on_top = True
            Window.resizable = False
        except Exception as e:
            print(f"窗口属性设置错误: {e}")

        sm = ScreenManager()
        sm.add_widget(MainScreen(name='main'))
        sm.add_widget(SecondScreen(name='second'))
        return sm


if __name__ == '__main__':

    os.system('"C:/Program Files/ZdBit/Queue/Server2012/getnob.exe"')
    time.sleep(3)

    # 捕获主应用程序异常
    try:
        HelloApp().run()
    except Exception as e:
        print(f"应用程序运行错误: {e}")